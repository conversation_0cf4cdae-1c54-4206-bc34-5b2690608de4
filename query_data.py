import argparse
import os
from dotenv import load_dotenv
from langchain_community.vectorstores import Chroma
from langchain.prompts import ChatPromptTemplate
from langchain_groq import ChatGroq 
from get_embedding_function import get_embedding_function

load_dotenv()

CHROMA_PATH = "chroma"

PROMPT_TEMPLATE = """
Answer the question based only on the following context:

{context}

---

Answer the question based on the above context: {question}
"""


def main():
    # Create CLI.
    parser = argparse.ArgumentParser()
    parser.add_argument("query_text", type=str, help="The query text.")
    args = parser.parse_args()
    query_text = args.query_text
    print(f"Query: {query_text}")   
    query_rag(query_text)


def query_rag(query_text: str):
    # Prepare the DB.
    embedding_function = get_embedding_function()
    
    db = Chroma(persist_directory=CHROMA_PATH, embedding_function=embedding_function)
    print("Number of documents:", db._collection.count()) 
    # Search the DB.
    results = db.similarity_search_with_score(query_text, k=5)

    context_text = "\n\n---\n\n".join([doc.page_content for doc, _score in results])
    prompt_template = ChatPromptTemplate.from_template(PROMPT_TEMPLATE)
    prompt = prompt_template.format(context=context_text, question=query_text)

    # ✅ Initialize Groq LLM (replace with your Groq model name like "llama3-70b-8192" or "mixtral-8x7b-32768")
    model = ChatGroq(
        model_name="meta-llama/llama-4-scout-17b-16e-instruct",
        api_key=os.environ["GROQ_API_KEY"]  # Loaded from .env file
    )

    response_text = model.invoke(prompt)

    # Extract source IDs from metadata
    sources = [doc.metadata.get("id", "Unknown source") for doc, _score in results]

    # Create structured output
    structured_output = f"""
    📌 Query:
    {query_text}

    📚 Sources Retrieved ({len(sources)}):
    """ + "\n".join(f"- {src}" for src in sources) + f"""

    💬 Answer:
    {response_text.content.strip()}
    """

    print(structured_output)
    return response_text

if __name__ == "__main__":
    main()
